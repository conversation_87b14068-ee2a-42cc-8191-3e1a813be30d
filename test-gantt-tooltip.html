<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GanttChart Tooltip</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            padding: 20px;
        }
    </style>
</head>
<body>
    <h1>GanttChart Tooltip Test</h1>
    <div class="test-container">
        <p>This is a test file to verify the GanttChart tooltip implementation.</p>
        <p>The tooltip should appear when hovering over bars that have tooltip data.</p>
        <p>Bars without tooltip data should work as before.</p>
        
        <h3>Expected behavior:</h3>
        <ul>
            <li>✅ Bars maintain their original positioning (CSS Grid)</li>
            <li>✅ Tooltip appears on hover for bars with tooltip data</li>
            <li>✅ Tooltip shows title (bar label) and custom information</li>
            <li>✅ Bars without tooltip data work normally</li>
            <li>✅ Horizontal scroll synchronization is preserved</li>
            <li>✅ No onClick functionality (removed as requested)</li>
        </ul>
    </div>
</body>
</html>
