<template>
	<div class="farm-gantt-chart" :style="componentStyle">
		<div class="farm-gantt-chart__header">
			<div class="farm-gantt-chart__row-label-space"/>
			<div class="farm-gantt-chart__timeline" :style="timelineGridStyle">
				<div
					v-for="(month, index) in monthColumns"
					:key="index"
					class="farm-gantt-chart__month-header"
					:class="{ 'farm-gantt-chart__month-header--current': month.isCurrentMonth }"
				>
					<farm-typography
						size="md"
						:weight="500"
						:color="month.isCurrentMonth ? 'primary' : 'black'"
						:color-variation="month.isCurrentMonth ? '' : '50'"
						class="mb-0"
					>
						{{ month.label }}
					</farm-typography>
				</div>
			</div>
		</div>

		<!-- Gantt Chart Content --> 
		<div class="farm-gantt-chart__content">
			<div
				v-for="(group, groupIndex) in data.groups"
				:key="'group-' + groupIndex"
				class="farm-gantt-chart__group"
			>
				<!-- Group label -->
				<div class="farm-gantt-chart__group-label">
					<farm-typography :weight="500">
						{{ group.title }}
					</farm-typography>
				</div>

				<!-- Group timeline with grid and bars -->
				<div class="farm-gantt-chart__group-timeline" :style="timelineGridStyle">
					<!-- Bars positioned using CSS Grid -->
					<div
						v-for="(bar, barIndex) in getPositionedBars(group.bars)"
						:key="'bar-' + barIndex"
						class="farm-gantt-chart__bar"
						:style="getBarGridStyle(bar)"
					>
						<!-- Bar with tooltip -->
						<farm-tooltip
							v-if="bar.tooltip && bar.tooltip.length > 0"
							color="gray"
						>
							<div class="farm-gantt-chart__tooltip-content">
								<div class="farm-gantt-chart__tooltip-title">{{ bar.label }}</div>
								<div
									v-for="(info, infoIndex) in bar.tooltip"
									:key="infoIndex"
									class="farm-gantt-chart__tooltip-item"
								>
									<strong>{{ info.label }}:</strong> {{ info.value }}
								</div>
							</div>
							<template v-slot:activator>
								<div class="farm-gantt-chart__bar-content">
									<farm-typography size="md" :weight="500" color="white" class="mb-0" ellipsis>
										{{ bar.label }}
									</farm-typography>
								</div>
							</template>
						</farm-tooltip>

						<!-- Bar without tooltip -->
						<div
							v-else
							class="farm-gantt-chart__bar-content"
						>
							<farm-typography size="md" :weight="500" color="white" class="mb-0" ellipsis>
								{{ bar.label }}
							</farm-typography>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Legend -->
		<div class="farm-gantt-chart__legend" v-if="autoGeneratedLegend.length > 0" :style="legendStyle">
			<div class="farm-gantt-chart__legend-title">
				<farm-typography size="md" :weight="700" color="black" color-variation="50">
					Legenda:
				</farm-typography>
			</div>
			<div
				v-for="(item, index) in autoGeneratedLegend"
				:key="'legend-' + index"
				class="farm-gantt-chart__legend-item"
			>
				<div
					class="farm-gantt-chart__legend-color"
					:style="{ backgroundColor: item.color }"
				></div>
				<div class="farm-gantt-chart__legend-label">
					<farm-typography size="md" color="black" color-variation="50">
						{{ item.label }}
					</farm-typography>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType, computed } from 'vue';
import type { GanttData } from './types';
import { getMonthsBetween, formatMonth, isCurrentMonth, getColumnForDate } from './utils/dateUtils';
import { buildGanttData, buildBarPositioning } from './composition';
import FarmTooltip from '../Tooltip/Tooltip.vue';

export default defineComponent({
	name: 'farm-gantt-chart',
	components: {
		FarmTooltip,
	},
	props: {
		data: {
			type: Object as PropType<GanttData>,
			required: true,
		},
	},
	setup(props) {
		// SECTION: Data Processing & Range Calculation
		const { autoCalculatedDateRange, autoGeneratedLegend } = buildGanttData(props);

		// SECTION: Timeline Generation
		const monthColumns = computed(() => {
			const { start, end } = autoCalculatedDateRange.value;
			const months = getMonthsBetween(start, end);
			return months.map(month => ({
				date: month,
				label: formatMonth(month),
				isCurrentMonth: isCurrentMonth(month),
			}));
		});

		const timelineGridStyle = computed(() => ({
			gridTemplateColumns: `repeat(${monthColumns.value.length}, 80px)`,
		}));

		const todayColumn = computed(() => {
			const today = new Date();
			const { start } = autoCalculatedDateRange.value;
			const column = getColumnForDate(today, start);
			return column >= 0 && column < monthColumns.value.length ? column : -1;
		});

		// SECTION: Bar Positioning & Styling
		const { getBarGridStyle, getPositionedBars } = buildBarPositioning(
			autoCalculatedDateRange,
			monthColumns
		);

		// SECTION: Layout & Styling
		const contentHeight = computed(() => {
			let totalHeight = 0;
			props.data.groups.forEach(group => {
				const positionedBars = getPositionedBars(group.bars);
				const maxRows = Math.max(
					1,
					...positionedBars.map(bar => (bar.rowPosition || 0) + 1)
				);
				const groupHeight = Math.max(60, maxRows * 35 + 10);
				totalHeight += groupHeight + 20;
			});
			return totalHeight;
		});

		const componentStyle = computed(() => ({
			'--gantt-content-height': `${contentHeight.value}px`,
		}));

		const legendStyle = computed(() => {
			const timelineWidth = monthColumns.value.length * 80;
			const totalWidth = 120 + timelineWidth;
			return {
				width: `${totalWidth}px`,
			};
		});

		return {
			monthColumns,
			timelineGridStyle,
			todayColumn,
			autoGeneratedLegend,
			legendStyle,
			getBarGridStyle,
			getPositionedBars,
			componentStyle,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './GanttChart';
</style>
